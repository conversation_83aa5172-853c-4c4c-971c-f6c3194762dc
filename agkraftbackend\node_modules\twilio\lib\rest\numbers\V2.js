"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const authorizationDocument_1 = require("./v2/authorizationDocument");
const bulkHostedNumberOrder_1 = require("./v2/bulkHostedNumberOrder");
const bundleClone_1 = require("./v2/bundleClone");
const hostedNumberOrder_1 = require("./v2/hostedNumberOrder");
const regulatoryCompliance_1 = require("./v2/regulatoryCompliance");
class V2 extends Version_1.default {
    /**
     * Initialize the V2 version of Numbers
     *
     * @param domain - The Twilio (Twilio.Numbers) domain
     */
    constructor(domain) {
        super(domain, "v2");
    }
    /** Getter for authorizationDocuments resource */
    get authorizationDocuments() {
        this._authorizationDocuments =
            this._authorizationDocuments || (0, authorizationDocument_1.AuthorizationDocumentListInstance)(this);
        return this._authorizationDocuments;
    }
    /** Getter for bulkHostedNumberOrders resource */
    get bulkHostedNumberOrders() {
        this._bulkHostedNumberOrders =
            this._bulkHostedNumberOrders || (0, bulkHostedNumberOrder_1.BulkHostedNumberOrderListInstance)(this);
        return this._bulkHostedNumberOrders;
    }
    /** Getter for bundleClone resource */
    get bundleClone() {
        this._bundleClone = this._bundleClone || (0, bundleClone_1.BundleCloneListInstance)(this);
        return this._bundleClone;
    }
    /** Getter for hostedNumberOrders resource */
    get hostedNumberOrders() {
        this._hostedNumberOrders =
            this._hostedNumberOrders || (0, hostedNumberOrder_1.HostedNumberOrderListInstance)(this);
        return this._hostedNumberOrders;
    }
    /** Getter for regulatoryCompliance resource */
    get regulatoryCompliance() {
        this._regulatoryCompliance =
            this._regulatoryCompliance || (0, regulatoryCompliance_1.RegulatoryComplianceListInstance)(this);
        return this._regulatoryCompliance;
    }
}
exports.default = V2;
