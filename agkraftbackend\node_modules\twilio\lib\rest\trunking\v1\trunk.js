"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Trunking
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TrunkPage = exports.TrunkListInstance = exports.TrunkInstance = exports.TrunkContextImpl = void 0;
const util_1 = require("util");
const Page_1 = __importDefault(require("../../../base/Page"));
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
const credentialList_1 = require("./trunk/credentialList");
const ipAccessControlList_1 = require("./trunk/ipAccessControlList");
const originationUrl_1 = require("./trunk/originationUrl");
const phoneNumber_1 = require("./trunk/phoneNumber");
const recording_1 = require("./trunk/recording");
class TrunkContextImpl {
    constructor(_version, sid) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sid)) {
            throw new Error("Parameter 'sid' is not valid.");
        }
        this._solution = { sid };
        this._uri = `/Trunks/${sid}`;
    }
    get credentialsLists() {
        this._credentialsLists =
            this._credentialsLists ||
                (0, credentialList_1.CredentialListListInstance)(this._version, this._solution.sid);
        return this._credentialsLists;
    }
    get ipAccessControlLists() {
        this._ipAccessControlLists =
            this._ipAccessControlLists ||
                (0, ipAccessControlList_1.IpAccessControlListListInstance)(this._version, this._solution.sid);
        return this._ipAccessControlLists;
    }
    get originationUrls() {
        this._originationUrls =
            this._originationUrls ||
                (0, originationUrl_1.OriginationUrlListInstance)(this._version, this._solution.sid);
        return this._originationUrls;
    }
    get phoneNumbers() {
        this._phoneNumbers =
            this._phoneNumbers ||
                (0, phoneNumber_1.PhoneNumberListInstance)(this._version, this._solution.sid);
        return this._phoneNumbers;
    }
    get recordings() {
        this._recordings =
            this._recordings ||
                (0, recording_1.RecordingListInstance)(this._version, this._solution.sid);
        return this._recordings;
    }
    remove(callback) {
        const headers = {};
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.remove({
            uri: instance._uri,
            method: "delete",
            headers,
        });
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TrunkInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["domainName"] !== undefined)
            data["DomainName"] = params["domainName"];
        if (params["disasterRecoveryUrl"] !== undefined)
            data["DisasterRecoveryUrl"] = params["disasterRecoveryUrl"];
        if (params["disasterRecoveryMethod"] !== undefined)
            data["DisasterRecoveryMethod"] = params["disasterRecoveryMethod"];
        if (params["transferMode"] !== undefined)
            data["TransferMode"] = params["transferMode"];
        if (params["secure"] !== undefined)
            data["Secure"] = serialize.bool(params["secure"]);
        if (params["cnamLookupEnabled"] !== undefined)
            data["CnamLookupEnabled"] = serialize.bool(params["cnamLookupEnabled"]);
        if (params["transferCallerId"] !== undefined)
            data["TransferCallerId"] = params["transferCallerId"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TrunkInstance(operationVersion, payload, instance._solution.sid));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TrunkContextImpl = TrunkContextImpl;
class TrunkInstance {
    constructor(_version, payload, sid) {
        this._version = _version;
        this.accountSid = payload.account_sid;
        this.domainName = payload.domain_name;
        this.disasterRecoveryMethod = payload.disaster_recovery_method;
        this.disasterRecoveryUrl = payload.disaster_recovery_url;
        this.friendlyName = payload.friendly_name;
        this.secure = payload.secure;
        this.recording = payload.recording;
        this.transferMode = payload.transfer_mode;
        this.transferCallerId = payload.transfer_caller_id;
        this.cnamLookupEnabled = payload.cnam_lookup_enabled;
        this.authType = payload.auth_type;
        this.symmetricRtpEnabled = payload.symmetric_rtp_enabled;
        this.authTypeSet = payload.auth_type_set;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this.sid = payload.sid;
        this.url = payload.url;
        this.links = payload.links;
        this._solution = { sid: sid || this.sid };
    }
    get _proxy() {
        this._context =
            this._context || new TrunkContextImpl(this._version, this._solution.sid);
        return this._context;
    }
    /**
     * Remove a TrunkInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed boolean
     */
    remove(callback) {
        return this._proxy.remove(callback);
    }
    /**
     * Fetch a TrunkInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed TrunkInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Access the credentialsLists.
     */
    credentialsLists() {
        return this._proxy.credentialsLists;
    }
    /**
     * Access the ipAccessControlLists.
     */
    ipAccessControlLists() {
        return this._proxy.ipAccessControlLists;
    }
    /**
     * Access the originationUrls.
     */
    originationUrls() {
        return this._proxy.originationUrls;
    }
    /**
     * Access the phoneNumbers.
     */
    phoneNumbers() {
        return this._proxy.phoneNumbers;
    }
    /**
     * Access the recordings.
     */
    recordings() {
        return this._proxy.recordings;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accountSid: this.accountSid,
            domainName: this.domainName,
            disasterRecoveryMethod: this.disasterRecoveryMethod,
            disasterRecoveryUrl: this.disasterRecoveryUrl,
            friendlyName: this.friendlyName,
            secure: this.secure,
            recording: this.recording,
            transferMode: this.transferMode,
            transferCallerId: this.transferCallerId,
            cnamLookupEnabled: this.cnamLookupEnabled,
            authType: this.authType,
            symmetricRtpEnabled: this.symmetricRtpEnabled,
            authTypeSet: this.authTypeSet,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
            sid: this.sid,
            url: this.url,
            links: this.links,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TrunkInstance = TrunkInstance;
function TrunkListInstance(version) {
    const instance = ((sid) => instance.get(sid));
    instance.get = function get(sid) {
        return new TrunkContextImpl(version, sid);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Trunks`;
    instance.create = function create(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        if (params["domainName"] !== undefined)
            data["DomainName"] = params["domainName"];
        if (params["disasterRecoveryUrl"] !== undefined)
            data["DisasterRecoveryUrl"] = params["disasterRecoveryUrl"];
        if (params["disasterRecoveryMethod"] !== undefined)
            data["DisasterRecoveryMethod"] = params["disasterRecoveryMethod"];
        if (params["transferMode"] !== undefined)
            data["TransferMode"] = params["transferMode"];
        if (params["secure"] !== undefined)
            data["Secure"] = serialize.bool(params["secure"]);
        if (params["cnamLookupEnabled"] !== undefined)
            data["CnamLookupEnabled"] = serialize.bool(params["cnamLookupEnabled"]);
        if (params["transferCallerId"] !== undefined)
            data["TransferCallerId"] = params["transferCallerId"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TrunkInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.page = function page(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["pageSize"] !== undefined)
            data["PageSize"] = params["pageSize"];
        if (params.pageNumber !== undefined)
            data["Page"] = params.pageNumber;
        if (params.pageToken !== undefined)
            data["PageToken"] = params.pageToken;
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.page({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TrunkPage(operationVersion, payload, instance._solution));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.each = instance._version.each;
    instance.list = instance._version.list;
    instance.getPage = function getPage(targetUrl, callback) {
        const operationPromise = instance._version._domain.twilio.request({
            method: "get",
            uri: targetUrl,
        });
        let pagePromise = operationPromise.then((payload) => new TrunkPage(instance._version, payload, instance._solution));
        pagePromise = instance._version.setPromiseCallback(pagePromise, callback);
        return pagePromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.TrunkListInstance = TrunkListInstance;
class TrunkPage extends Page_1.default {
    /**
     * Initialize the TrunkPage
     *
     * @param version - Version of the resource
     * @param response - Response from the API
     * @param solution - Path solution
     */
    constructor(version, response, solution) {
        super(version, response, solution);
    }
    /**
     * Build an instance of TrunkInstance
     *
     * @param payload - Payload response from the API
     */
    getInstance(payload) {
        return new TrunkInstance(this._version, payload);
    }
    [util_1.inspect.custom](depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TrunkPage = TrunkPage;
