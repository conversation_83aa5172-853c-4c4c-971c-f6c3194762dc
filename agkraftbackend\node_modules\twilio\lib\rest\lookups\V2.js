"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Lookups
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const bucket_1 = require("./v2/bucket");
const lookupOverride_1 = require("./v2/lookupOverride");
const phoneNumber_1 = require("./v2/phoneNumber");
const query_1 = require("./v2/query");
const rateLimit_1 = require("./v2/rateLimit");
class V2 extends Version_1.default {
    /**
     * Initialize the V2 version of Lookups
     *
     * @param domain - The Twilio (Twilio.Lookups) domain
     */
    constructor(domain) {
        super(domain, "v2");
    }
    /** Getter for bucket resource */
    get bucket() {
        this._bucket = this._bucket || (0, bucket_1.BucketListInstance)(this);
        return this._bucket;
    }
    /** Getter for lookupOverrides resource */
    get lookupOverrides() {
        this._lookupOverrides =
            this._lookupOverrides || (0, lookupOverride_1.LookupOverrideListInstance)(this);
        return this._lookupOverrides;
    }
    /** Getter for phoneNumbers resource */
    get phoneNumbers() {
        this._phoneNumbers = this._phoneNumbers || (0, phoneNumber_1.PhoneNumberListInstance)(this);
        return this._phoneNumbers;
    }
    /** Getter for query resource */
    get query() {
        this._query = this._query || (0, query_1.QueryListInstance)(this);
        return this._query;
    }
    /** Getter for rateLimits resource */
    get rateLimits() {
        this._rateLimits = this._rateLimits || (0, rateLimit_1.RateLimitListInstance)(this);
        return this._rateLimits;
    }
}
exports.default = V2;
