/// <reference types="node" />
import { inspect, InspectOptions } from "util";
import V2 from "../V2";
export declare class CallForwarding {
    "call_forwarding_enabled"?: boolean;
    "error_code"?: number;
}
export declare class CallerName {
    "caller_name"?: string;
    "caller_type"?: string;
    "error_code"?: number;
}
export declare class IdentityMatch {
    "first_name_match"?: string;
    "last_name_match"?: string;
    "address_line_match"?: string;
    "city_match"?: string;
    "state_match"?: string;
    "postal_code_match"?: string;
    "country_code_match"?: string;
    "national_id_match"?: string;
    "date_of_birth_match"?: string;
    "summary_score"?: number;
    "error_code"?: number;
    "error_message"?: string;
}
export declare class IdentityMatchParameters {
    "first_name"?: string;
    "last_name"?: string;
    "address_line1"?: string;
    "address_line2"?: string;
    "city"?: string;
    "state"?: string;
    "postal_code"?: string;
    "address_country_code"?: string;
    "national_id"?: string;
    "date_of_birth"?: string;
}
export declare class LastSimSwap {
    "last_sim_swap_date"?: Date;
    "swapped_period"?: string;
    "swapped_in_period"?: boolean;
}
export declare class LineStatus {
    "status"?: string;
    "error_code"?: number;
}
export declare class LineTypeIntelligence {
    "type"?: string;
    "carrier_name"?: string;
    "mobile_country_code"?: string;
    "mobile_network_code"?: string;
    "error_code"?: number;
}
export declare class LookupRequest1 {
    "phone_numbers"?: Array<LookupRequestWithCorId>;
}
export declare class LookupRequestWithCorId {
    /**
     * Unique identifier used to match request with response
     */
    "correlation_id"?: string;
    "phone_number": string;
    "fields"?: Array<string>;
    "country_code"?: string;
    "identity_match"?: IdentityMatchParameters;
    "reassigned_number"?: ReassignedNumberRequest;
    "sms_pumping_risk"?: SmsPumpingRiskParameters;
}
export declare class LookupResponseWithCorId {
    /**
     * Unique identifier used to match request with response
     */
    "correlation_id"?: string;
    /**
     * Twilio error code in case that the request to downstream fails
     */
    "twilio_error_code"?: number;
    "calling_country_code"?: string;
    "country_code"?: string;
    "phone_number"?: string;
    "national_format"?: string;
    "valid"?: boolean;
    "validation_errors"?: Array<string>;
    "caller_name"?: CallerName;
    "sim_swap"?: SimSwap;
    "call_forwarding"?: CallForwarding;
    "line_type_intelligence"?: LineTypeIntelligence;
    "line_status"?: LineStatus;
    "identity_match"?: IdentityMatch;
    "reassigned_number"?: ReassignedNumberResponse;
    "sms_pumping_risk"?: SmsPumpingRisk;
}
export declare class ReassignedNumberRequest {
    "last_verified_date"?: string;
}
export declare class ReassignedNumberResponse {
    "last_verified_date"?: string;
    "is_number_reassigned"?: string;
    "error_code"?: string;
}
export declare class SimSwap {
    "last_sim_swap"?: LastSimSwap;
    "carrier_name"?: string;
    "mobile_country_code"?: string;
    "mobile_network_code"?: string;
    "error_code"?: number;
}
export declare class SmsPumpingRisk {
    "carrier_risk_category"?: string;
    "number_blocked"?: boolean;
    "number_blocked_date"?: Date;
    "number_blocked_last_3_months"?: boolean;
    "sms_pumping_risk_score"?: number;
    "error_code"?: number;
}
export declare class SmsPumpingRiskParameters {
    "partner_sub_id"?: string;
}
/**
 * Options to pass to create a QueryInstance
 */
export interface QueryListInstanceCreateOptions {
    /**  */
    lookupRequest1?: LookupRequest1;
}
export interface QuerySolution {
}
export interface QueryListInstance {
    _version: V2;
    _solution: QuerySolution;
    _uri: string;
    /**
     * Create a QueryInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed QueryInstance
     */
    create(callback?: (error: Error | null, item?: QueryInstance) => any): Promise<QueryInstance>;
    /**
     * Create a QueryInstance
     *
     * @param params - Body for request
     * @param headers - header params for request
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed QueryInstance
     */
    create(params: LookupRequest1, headers?: any, callback?: (error: Error | null, item?: QueryInstance) => any): Promise<QueryInstance>;
    /**
     * Provide a user-friendly representation
     */
    toJSON(): any;
    [inspect.custom](_depth: any, options: InspectOptions): any;
}
export declare function QueryListInstance(version: V2): QueryListInstance;
interface QueryResource {
    phone_numbers: Array<LookupResponseWithCorId>;
}
export declare class QueryInstance {
    protected _version: V2;
    constructor(_version: V2, payload: QueryResource);
    phoneNumbers: Array<LookupResponseWithCorId>;
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON(): {
        phoneNumbers: LookupResponseWithCorId[];
    };
    [inspect.custom](_depth: any, options: InspectOptions): string;
}
export {};
