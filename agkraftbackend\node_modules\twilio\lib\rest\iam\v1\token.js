"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Iam
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.TokenInstance = exports.TokenListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
function TokenListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/token`;
    instance.create = function create(params, callback) {
        if (params === null || params === undefined) {
            throw new Error('Required parameter "params" missing.');
        }
        if (params["grantType"] === null || params["grantType"] === undefined) {
            throw new Error("Required parameter \"params['grantType']\" missing.");
        }
        if (params["clientId"] === null || params["clientId"] === undefined) {
            throw new Error("Required parameter \"params['clientId']\" missing.");
        }
        let data = {};
        data["grant_type"] = params["grantType"];
        data["client_id"] = params["clientId"];
        if (params["clientSecret"] !== undefined)
            data["client_secret"] = params["clientSecret"];
        if (params["code"] !== undefined)
            data["code"] = params["code"];
        if (params["redirectUri"] !== undefined)
            data["redirect_uri"] = params["redirectUri"];
        if (params["audience"] !== undefined)
            data["audience"] = params["audience"];
        if (params["refreshToken"] !== undefined)
            data["refresh_token"] = params["refreshToken"];
        if (params["scope"] !== undefined)
            data["scope"] = params["scope"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.create({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new TokenInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.TokenListInstance = TokenListInstance;
class TokenInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.accessToken = payload.access_token;
        this.refreshToken = payload.refresh_token;
        this.idToken = payload.id_token;
        this.tokenType = payload.token_type;
        this.expiresIn = payload.expires_in;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            accessToken: this.accessToken,
            refreshToken: this.refreshToken,
            idToken: this.idToken,
            tokenType: this.tokenType,
            expiresIn: this.expiresIn,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.TokenInstance = TokenInstance;
