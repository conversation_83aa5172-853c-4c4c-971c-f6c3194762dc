"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Intelligence
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const customOperator_1 = require("./v2/customOperator");
const operator_1 = require("./v2/operator");
const operatorAttachment_1 = require("./v2/operatorAttachment");
const operatorAttachments_1 = require("./v2/operatorAttachments");
const operatorType_1 = require("./v2/operatorType");
const prebuiltOperator_1 = require("./v2/prebuiltOperator");
const service_1 = require("./v2/service");
const transcript_1 = require("./v2/transcript");
class V2 extends Version_1.default {
    /**
     * Initialize the V2 version of Intelligence
     *
     * @param domain - The Twilio (Twilio.Intelligence) domain
     */
    constructor(domain) {
        super(domain, "v2");
    }
    /** Getter for customOperators resource */
    get customOperators() {
        this._customOperators =
            this._customOperators || (0, customOperator_1.CustomOperatorListInstance)(this);
        return this._customOperators;
    }
    /** Getter for operators resource */
    get operators() {
        this._operators = this._operators || (0, operator_1.OperatorListInstance)(this);
        return this._operators;
    }
    /** Getter for operatorAttachment resource */
    get operatorAttachment() {
        this._operatorAttachment =
            this._operatorAttachment || (0, operatorAttachment_1.OperatorAttachmentListInstance)(this);
        return this._operatorAttachment;
    }
    /** Getter for operatorAttachments resource */
    get operatorAttachments() {
        this._operatorAttachments =
            this._operatorAttachments || (0, operatorAttachments_1.OperatorAttachmentsListInstance)(this);
        return this._operatorAttachments;
    }
    /** Getter for operatorType resource */
    get operatorType() {
        this._operatorType = this._operatorType || (0, operatorType_1.OperatorTypeListInstance)(this);
        return this._operatorType;
    }
    /** Getter for prebuiltOperators resource */
    get prebuiltOperators() {
        this._prebuiltOperators =
            this._prebuiltOperators || (0, prebuiltOperator_1.PrebuiltOperatorListInstance)(this);
        return this._prebuiltOperators;
    }
    /** Getter for services resource */
    get services() {
        this._services = this._services || (0, service_1.ServiceListInstance)(this);
        return this._services;
    }
    /** Getter for transcripts resource */
    get transcripts() {
        this._transcripts = this._transcripts || (0, transcript_1.TranscriptListInstance)(this);
        return this._transcripts;
    }
}
exports.default = V2;
