"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Routes
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SipDomainListInstance = exports.SipDomainInstance = exports.SipDomainContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
const utility_1 = require("../../../base/utility");
class SipDomainContextImpl {
    constructor(_version, sipDomain) {
        this._version = _version;
        if (!(0, utility_1.isValidPathParam)(sipDomain)) {
            throw new Error("Parameter 'sipDomain' is not valid.");
        }
        this._solution = { sipDomain };
        this._uri = `/SipDomains/${sipDomain}`;
    }
    fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SipDomainInstance(operationVersion, payload, instance._solution.sipDomain));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    update(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["voiceRegion"] !== undefined)
            data["VoiceRegion"] = params["voiceRegion"];
        if (params["friendlyName"] !== undefined)
            data["FriendlyName"] = params["friendlyName"];
        const headers = {};
        headers["Content-Type"] = "application/x-www-form-urlencoded";
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.update({
            uri: instance._uri,
            method: "post",
            data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new SipDomainInstance(operationVersion, payload, instance._solution.sipDomain));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SipDomainContextImpl = SipDomainContextImpl;
class SipDomainInstance {
    constructor(_version, payload, sipDomain) {
        this._version = _version;
        this.sipDomain = payload.sip_domain;
        this.url = payload.url;
        this.sid = payload.sid;
        this.accountSid = payload.account_sid;
        this.friendlyName = payload.friendly_name;
        this.voiceRegion = payload.voice_region;
        this.dateCreated = deserialize.iso8601DateTime(payload.date_created);
        this.dateUpdated = deserialize.iso8601DateTime(payload.date_updated);
        this._solution = { sipDomain: sipDomain || this.sipDomain };
    }
    get _proxy() {
        this._context =
            this._context ||
                new SipDomainContextImpl(this._version, this._solution.sipDomain);
        return this._context;
    }
    /**
     * Fetch a SipDomainInstance
     *
     * @param callback - Callback to handle processed record
     *
     * @returns Resolves to processed SipDomainInstance
     */
    fetch(callback) {
        return this._proxy.fetch(callback);
    }
    update(params, callback) {
        return this._proxy.update(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            sipDomain: this.sipDomain,
            url: this.url,
            sid: this.sid,
            accountSid: this.accountSid,
            friendlyName: this.friendlyName,
            voiceRegion: this.voiceRegion,
            dateCreated: this.dateCreated,
            dateUpdated: this.dateUpdated,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.SipDomainInstance = SipDomainInstance;
function SipDomainListInstance(version) {
    const instance = ((sipDomain) => instance.get(sipDomain));
    instance.get = function get(sipDomain) {
        return new SipDomainContextImpl(version, sipDomain);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.SipDomainListInstance = SipDomainListInstance;
