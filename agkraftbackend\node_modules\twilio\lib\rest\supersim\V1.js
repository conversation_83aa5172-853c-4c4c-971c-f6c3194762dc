"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Supersim
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const esimProfile_1 = require("./v1/esimProfile");
const fleet_1 = require("./v1/fleet");
const ipCommand_1 = require("./v1/ipCommand");
const network_1 = require("./v1/network");
const networkAccessProfile_1 = require("./v1/networkAccessProfile");
const settingsUpdate_1 = require("./v1/settingsUpdate");
const sim_1 = require("./v1/sim");
const smsCommand_1 = require("./v1/smsCommand");
const usageRecord_1 = require("./v1/usageRecord");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Supersim
     *
     * @param domain - The Twilio (Twilio.Supersim) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for esimProfiles resource */
    get esimProfiles() {
        this._esimProfiles = this._esimProfiles || (0, esimProfile_1.EsimProfileListInstance)(this);
        return this._esimProfiles;
    }
    /** Getter for fleets resource */
    get fleets() {
        this._fleets = this._fleets || (0, fleet_1.FleetListInstance)(this);
        return this._fleets;
    }
    /** Getter for ipCommands resource */
    get ipCommands() {
        this._ipCommands = this._ipCommands || (0, ipCommand_1.IpCommandListInstance)(this);
        return this._ipCommands;
    }
    /** Getter for networks resource */
    get networks() {
        this._networks = this._networks || (0, network_1.NetworkListInstance)(this);
        return this._networks;
    }
    /** Getter for networkAccessProfiles resource */
    get networkAccessProfiles() {
        this._networkAccessProfiles =
            this._networkAccessProfiles || (0, networkAccessProfile_1.NetworkAccessProfileListInstance)(this);
        return this._networkAccessProfiles;
    }
    /** Getter for settingsUpdates resource */
    get settingsUpdates() {
        this._settingsUpdates =
            this._settingsUpdates || (0, settingsUpdate_1.SettingsUpdateListInstance)(this);
        return this._settingsUpdates;
    }
    /** Getter for sims resource */
    get sims() {
        this._sims = this._sims || (0, sim_1.SimListInstance)(this);
        return this._sims;
    }
    /** Getter for smsCommands resource */
    get smsCommands() {
        this._smsCommands = this._smsCommands || (0, smsCommand_1.SmsCommandListInstance)(this);
        return this._smsCommands;
    }
    /** Getter for usageRecords resource */
    get usageRecords() {
        this._usageRecords = this._usageRecords || (0, usageRecord_1.UsageRecordListInstance)(this);
        return this._usageRecords;
    }
}
exports.default = V1;
