"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Verify
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.VerificationAttemptsSummaryListInstance = exports.VerificationAttemptsSummaryInstance = exports.VerificationAttemptsSummaryContextImpl = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
class VerificationAttemptsSummaryContextImpl {
    constructor(_version) {
        this._version = _version;
        this._solution = {};
        this._uri = `/Attempts/Summary`;
    }
    fetch(params, callback) {
        if (params instanceof Function) {
            callback = params;
            params = {};
        }
        else {
            params = params || {};
        }
        let data = {};
        if (params["verifyServiceSid"] !== undefined)
            data["VerifyServiceSid"] = params["verifyServiceSid"];
        if (params["dateCreatedAfter"] !== undefined)
            data["DateCreatedAfter"] = serialize.iso8601DateTime(params["dateCreatedAfter"]);
        if (params["dateCreatedBefore"] !== undefined)
            data["DateCreatedBefore"] = serialize.iso8601DateTime(params["dateCreatedBefore"]);
        if (params["country"] !== undefined)
            data["Country"] = params["country"];
        if (params["channel"] !== undefined)
            data["Channel"] = params["channel"];
        if (params["destinationPrefix"] !== undefined)
            data["DestinationPrefix"] = params["destinationPrefix"];
        const headers = {};
        headers["Accept"] = "application/json";
        const instance = this;
        let operationVersion = instance._version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            params: data,
            headers,
        });
        operationPromise = operationPromise.then((payload) => new VerificationAttemptsSummaryInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return this._solution;
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationAttemptsSummaryContextImpl = VerificationAttemptsSummaryContextImpl;
class VerificationAttemptsSummaryInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.totalAttempts = deserialize.integer(payload.total_attempts);
        this.totalConverted = deserialize.integer(payload.total_converted);
        this.totalUnconverted = deserialize.integer(payload.total_unconverted);
        this.conversionRatePercentage = payload.conversion_rate_percentage;
        this.url = payload.url;
        this._solution = {};
    }
    get _proxy() {
        this._context =
            this._context ||
                new VerificationAttemptsSummaryContextImpl(this._version);
        return this._context;
    }
    fetch(params, callback) {
        return this._proxy.fetch(params, callback);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            totalAttempts: this.totalAttempts,
            totalConverted: this.totalConverted,
            totalUnconverted: this.totalUnconverted,
            conversionRatePercentage: this.conversionRatePercentage,
            url: this.url,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.VerificationAttemptsSummaryInstance = VerificationAttemptsSummaryInstance;
function VerificationAttemptsSummaryListInstance(version) {
    const instance = (() => instance.get());
    instance.get = function get() {
        return new VerificationAttemptsSummaryContextImpl(version);
    };
    instance._version = version;
    instance._solution = {};
    instance._uri = ``;
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.VerificationAttemptsSummaryListInstance = VerificationAttemptsSummaryListInstance;
