"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Iam
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const apiKey_1 = require("./v1/apiKey");
const getApiKeys_1 = require("./v1/getApiKeys");
const newApiKey_1 = require("./v1/newApiKey");
const token_1 = require("./v1/token");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of Iam
     *
     * @param domain - The Twilio (Twilio.Iam) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for apiKey resource */
    get apiKey() {
        this._apiKey = this._apiKey || (0, apiKey_1.ApiKeyListInstance)(this);
        return this._apiKey;
    }
    /** Getter for getApiKeys resource */
    get getApiKeys() {
        this._getApiKeys = this._getApiKeys || (0, getApiKeys_1.GetApiKeysListInstance)(this);
        return this._getApiKeys;
    }
    /** Getter for newApiKey resource */
    get newApiKey() {
        this._newApiKey = this._newApiKey || (0, newApiKey_1.NewApiKeyListInstance)(this);
        return this._newApiKey;
    }
    /** Getter for token resource */
    get token() {
        this._token = this._token || (0, token_1.TokenListInstance)(this);
        return this._token;
    }
}
exports.default = V1;
