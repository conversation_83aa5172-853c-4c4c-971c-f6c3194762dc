"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Numbers
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.WebhookInstance = exports.WebhookListInstance = void 0;
const util_1 = require("util");
const deserialize = require("../../../base/deserialize");
const serialize = require("../../../base/serialize");
function WebhookListInstance(version) {
    const instance = {};
    instance._version = version;
    instance._solution = {};
    instance._uri = `/Porting/Configuration/Webhook`;
    instance.fetch = function fetch(callback) {
        const headers = {};
        headers["Accept"] = "application/json";
        let operationVersion = version, operationPromise = operationVersion.fetch({
            uri: instance._uri,
            method: "get",
            headers,
        });
        operationPromise = operationPromise.then((payload) => new WebhookInstance(operationVersion, payload));
        operationPromise = instance._version.setPromiseCallback(operationPromise, callback);
        return operationPromise;
    };
    instance.toJSON = function toJSON() {
        return instance._solution;
    };
    instance[util_1.inspect.custom] = function inspectImpl(_depth, options) {
        return (0, util_1.inspect)(instance.toJSON(), options);
    };
    return instance;
}
exports.WebhookListInstance = WebhookListInstance;
class WebhookInstance {
    constructor(_version, payload) {
        this._version = _version;
        this.url = payload.url;
        this.portInTargetUrl = payload.port_in_target_url;
        this.portOutTargetUrl = payload.port_out_target_url;
        this.notificationsOf = payload.notifications_of;
        this.portInTargetDateCreated = deserialize.iso8601DateTime(payload.port_in_target_date_created);
        this.portOutTargetDateCreated = deserialize.iso8601DateTime(payload.port_out_target_date_created);
    }
    /**
     * Provide a user-friendly representation
     *
     * @returns Object
     */
    toJSON() {
        return {
            url: this.url,
            portInTargetUrl: this.portInTargetUrl,
            portOutTargetUrl: this.portOutTargetUrl,
            notificationsOf: this.notificationsOf,
            portInTargetDateCreated: this.portInTargetDateCreated,
            portOutTargetDateCreated: this.portOutTargetDateCreated,
        };
    }
    [util_1.inspect.custom](_depth, options) {
        return (0, util_1.inspect)(this.toJSON(), options);
    }
}
exports.WebhookInstance = WebhookInstance;
