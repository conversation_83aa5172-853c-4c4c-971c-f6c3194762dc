"use strict";
/*
 * This code was generated by
 * ___ _ _ _ _ _    _ ____    ____ ____ _    ____ ____ _  _ ____ ____ ____ ___ __   __
 *  |  | | | | |    | |  | __ |  | |__| | __ | __ |___ |\ | |___ |__/ |__|  | |  | |__/
 *  |  |_|_| | |___ | |__|    |__| |  | |    |__] |___ | \| |___ |  \ |  |  | |__| |  \
 *
 * Twilio - Flex
 * This is the public Twilio REST API.
 *
 * NOTE: This class is auto generated by OpenAPI Generator.
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const Version_1 = __importDefault(require("../../base/Version"));
const assessments_1 = require("./v1/assessments");
const channel_1 = require("./v1/channel");
const configuration_1 = require("./v1/configuration");
const flexFlow_1 = require("./v1/flexFlow");
const insightsAssessmentsComment_1 = require("./v1/insightsAssessmentsComment");
const insightsConversations_1 = require("./v1/insightsConversations");
const insightsQuestionnaires_1 = require("./v1/insightsQuestionnaires");
const insightsQuestionnairesCategory_1 = require("./v1/insightsQuestionnairesCategory");
const insightsQuestionnairesQuestion_1 = require("./v1/insightsQuestionnairesQuestion");
const insightsSegments_1 = require("./v1/insightsSegments");
const insightsSession_1 = require("./v1/insightsSession");
const insightsSettingsAnswerSets_1 = require("./v1/insightsSettingsAnswerSets");
const insightsSettingsComment_1 = require("./v1/insightsSettingsComment");
const insightsUserRoles_1 = require("./v1/insightsUserRoles");
const interaction_1 = require("./v1/interaction");
const plugin_1 = require("./v1/plugin");
const pluginArchive_1 = require("./v1/pluginArchive");
const pluginConfiguration_1 = require("./v1/pluginConfiguration");
const pluginConfigurationArchive_1 = require("./v1/pluginConfigurationArchive");
const pluginRelease_1 = require("./v1/pluginRelease");
const pluginVersionArchive_1 = require("./v1/pluginVersionArchive");
const provisioningStatus_1 = require("./v1/provisioningStatus");
const webChannel_1 = require("./v1/webChannel");
class V1 extends Version_1.default {
    /**
     * Initialize the V1 version of FlexApi
     *
     * @param domain - The Twilio (Twilio.FlexApi) domain
     */
    constructor(domain) {
        super(domain, "v1");
    }
    /** Getter for assessments resource */
    get assessments() {
        this._assessments = this._assessments || (0, assessments_1.AssessmentsListInstance)(this);
        return this._assessments;
    }
    /** Getter for channel resource */
    get channel() {
        this._channel = this._channel || (0, channel_1.ChannelListInstance)(this);
        return this._channel;
    }
    /** Getter for configuration resource */
    get configuration() {
        this._configuration =
            this._configuration || (0, configuration_1.ConfigurationListInstance)(this);
        return this._configuration;
    }
    /** Getter for flexFlow resource */
    get flexFlow() {
        this._flexFlow = this._flexFlow || (0, flexFlow_1.FlexFlowListInstance)(this);
        return this._flexFlow;
    }
    /** Getter for insightsAssessmentsComment resource */
    get insightsAssessmentsComment() {
        this._insightsAssessmentsComment =
            this._insightsAssessmentsComment ||
                (0, insightsAssessmentsComment_1.InsightsAssessmentsCommentListInstance)(this);
        return this._insightsAssessmentsComment;
    }
    /** Getter for insightsConversations resource */
    get insightsConversations() {
        this._insightsConversations =
            this._insightsConversations || (0, insightsConversations_1.InsightsConversationsListInstance)(this);
        return this._insightsConversations;
    }
    /** Getter for insightsQuestionnaires resource */
    get insightsQuestionnaires() {
        this._insightsQuestionnaires =
            this._insightsQuestionnaires || (0, insightsQuestionnaires_1.InsightsQuestionnairesListInstance)(this);
        return this._insightsQuestionnaires;
    }
    /** Getter for insightsQuestionnairesCategory resource */
    get insightsQuestionnairesCategory() {
        this._insightsQuestionnairesCategory =
            this._insightsQuestionnairesCategory ||
                (0, insightsQuestionnairesCategory_1.InsightsQuestionnairesCategoryListInstance)(this);
        return this._insightsQuestionnairesCategory;
    }
    /** Getter for insightsQuestionnairesQuestion resource */
    get insightsQuestionnairesQuestion() {
        this._insightsQuestionnairesQuestion =
            this._insightsQuestionnairesQuestion ||
                (0, insightsQuestionnairesQuestion_1.InsightsQuestionnairesQuestionListInstance)(this);
        return this._insightsQuestionnairesQuestion;
    }
    /** Getter for insightsSegments resource */
    get insightsSegments() {
        this._insightsSegments =
            this._insightsSegments || (0, insightsSegments_1.InsightsSegmentsListInstance)(this);
        return this._insightsSegments;
    }
    /** Getter for insightsSession resource */
    get insightsSession() {
        this._insightsSession =
            this._insightsSession || (0, insightsSession_1.InsightsSessionListInstance)(this);
        return this._insightsSession;
    }
    /** Getter for insightsSettingsAnswerSets resource */
    get insightsSettingsAnswerSets() {
        this._insightsSettingsAnswerSets =
            this._insightsSettingsAnswerSets ||
                (0, insightsSettingsAnswerSets_1.InsightsSettingsAnswerSetsListInstance)(this);
        return this._insightsSettingsAnswerSets;
    }
    /** Getter for insightsSettingsComment resource */
    get insightsSettingsComment() {
        this._insightsSettingsComment =
            this._insightsSettingsComment ||
                (0, insightsSettingsComment_1.InsightsSettingsCommentListInstance)(this);
        return this._insightsSettingsComment;
    }
    /** Getter for insightsUserRoles resource */
    get insightsUserRoles() {
        this._insightsUserRoles =
            this._insightsUserRoles || (0, insightsUserRoles_1.InsightsUserRolesListInstance)(this);
        return this._insightsUserRoles;
    }
    /** Getter for interaction resource */
    get interaction() {
        this._interaction = this._interaction || (0, interaction_1.InteractionListInstance)(this);
        return this._interaction;
    }
    /** Getter for plugins resource */
    get plugins() {
        this._plugins = this._plugins || (0, plugin_1.PluginListInstance)(this);
        return this._plugins;
    }
    /** Getter for pluginArchive resource */
    get pluginArchive() {
        this._pluginArchive =
            this._pluginArchive || (0, pluginArchive_1.PluginArchiveListInstance)(this);
        return this._pluginArchive;
    }
    /** Getter for pluginConfigurations resource */
    get pluginConfigurations() {
        this._pluginConfigurations =
            this._pluginConfigurations || (0, pluginConfiguration_1.PluginConfigurationListInstance)(this);
        return this._pluginConfigurations;
    }
    /** Getter for pluginConfigurationArchive resource */
    get pluginConfigurationArchive() {
        this._pluginConfigurationArchive =
            this._pluginConfigurationArchive ||
                (0, pluginConfigurationArchive_1.PluginConfigurationArchiveListInstance)(this);
        return this._pluginConfigurationArchive;
    }
    /** Getter for pluginReleases resource */
    get pluginReleases() {
        this._pluginReleases =
            this._pluginReleases || (0, pluginRelease_1.PluginReleaseListInstance)(this);
        return this._pluginReleases;
    }
    /** Getter for pluginVersionArchive resource */
    get pluginVersionArchive() {
        this._pluginVersionArchive =
            this._pluginVersionArchive || (0, pluginVersionArchive_1.PluginVersionArchiveListInstance)(this);
        return this._pluginVersionArchive;
    }
    /** Getter for provisioningStatus resource */
    get provisioningStatus() {
        this._provisioningStatus =
            this._provisioningStatus || (0, provisioningStatus_1.ProvisioningStatusListInstance)(this);
        return this._provisioningStatus;
    }
    /** Getter for webChannel resource */
    get webChannel() {
        this._webChannel = this._webChannel || (0, webChannel_1.WebChannelListInstance)(this);
        return this._webChannel;
    }
}
exports.default = V1;
