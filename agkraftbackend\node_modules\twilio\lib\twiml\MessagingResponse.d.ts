/**
 * This code was generated by
 * \ / _    _  _|   _  _
 *  | (_)\/(_)(_|\/| |(/_  v1.0.0
 *       /       /
 */
import { XMLElement } from "xmlbuilder";
import TwiML from "./TwiML";
declare class MessagingResponse extends TwiML {
    /**
     * <Response> TwiML for Messages
     */
    constructor();
    /**
     * Comments in <Response>
     *
     * @param comment - XML Comment
     */
    comment(comment: string): XMLElement;
    /**
     * Comments after <Response>
     *
     * @param comment - XML Comment
     */
    commentAfter(comment: string): XMLElement;
    /**
     * Comments before <Response>
     *
     * @param comment - XML Comment
     */
    commentBefore(comment: string): XMLElement;
    /**
     * <Message> TwiML Verb
     *
     * @param attributes - TwiML attributes
     * @param body - Message Body
     */
    message(body: string): MessagingResponse.Message;
    message(attributes: MessagingResponse.MessageAttributes, body: string): MessagingResponse.Message;
    /**
     * <Redirect> TwiML Verb
     *
     * @param attributes - TwiML attributes
     * @param url - Redirect URL
     */
    redirect(url: string): MessagingResponse.Redirect;
    redirect(attributes: MessagingResponse.RedirectAttributes, url: string): MessagingResponse.Redirect;
}
declare namespace MessagingResponse {
    /**
     * Attributes to pass to message
     */
    interface MessageAttributes {
        /** action - A URL specifying where Twilio should send status callbacks for the created outbound message. */
        action?: string;
        /** from - Phone Number to send Message from */
        from?: string;
        /** method - Action URL Method */
        method?: string;
        /** statusCallback - Status callback URL. Deprecated in favor of action. */
        statusCallback?: string;
        /** to - Phone Number to send Message to */
        to?: string;
    }
    /**
     * Attributes to pass to redirect
     */
    interface RedirectAttributes {
        /** method - Redirect URL method */
        method?: string;
    }
    class Body extends TwiML {
        body: XMLElement;
        /**
         * <Body> TwiML Noun
         */
        constructor(body: XMLElement);
    }
    class Media extends TwiML {
        media: XMLElement;
        /**
         * <Media> TwiML Noun
         */
        constructor(media: XMLElement);
    }
    class Message extends TwiML {
        message: XMLElement;
        /**
         * <Message> TwiML Verb
         */
        constructor(message: XMLElement);
        /**
         * <Body> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param message - Message Body
         */
        body(message: string): MessagingResponse.Body;
        body(attributes: object, message: string): MessagingResponse.Body;
        /**
         * <Media> TwiML Noun
         *
         * @param attributes - TwiML attributes
         * @param url - Media URL
         */
        media(url: string): MessagingResponse.Media;
        media(attributes: object, url: string): MessagingResponse.Media;
    }
    class Redirect extends TwiML {
        redirect: XMLElement;
        /**
         * <Redirect> TwiML Verb
         */
        constructor(redirect: XMLElement);
    }
}
export = MessagingResponse;
